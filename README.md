# Cassidy2API - OpenAI Compatible API

这是一个兼容 OpenAI API 的 Go 服务，可以让第三方应用通过标准的 OpenAI API 接口调用您的服务。

## 功能特性

- ✅ 完全兼容 OpenAI API 格式
- ✅ 支持聊天完成接口 (`/v1/chat/completions`)
- ✅ 支持模型列表接口 (`/v1/models`)
- ✅ 支持流式和非流式响应
- ✅ API 密钥认证
- ✅ CORS 支持
- ✅ 请求日志记录
- ✅ 错误处理

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

### 3. 测试 API

#### 健康检查

```bash
curl http://localhost:8080/health
```

#### 获取模型列表

```bash
curl http://localhost:8080/v1/models
```

#### 聊天完成（需要 API 密钥）

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-test123456789" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }'
```

## API 接口

### 1. 聊天完成 - `/v1/chat/completions`

**请求方法**: POST  
**认证**: 需要 Bearer Token

**请求体**:
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello!"
    }
  ],
  "max_tokens": 100,
  "temperature": 0.7,
  "stream": false
}
```

**响应**:
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### 2. 模型列表 - `/v1/models`

**请求方法**: GET  
**认证**: 不需要

**响应**:
```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-3.5-turbo",
      "object": "model",
      "created": **********,
      "owned_by": "cassidy2api"
    }
  ]
}
```

## 认证

API 使用 Bearer Token 认证。在请求头中包含：

```
Authorization: Bearer your-api-key
```

### 默认测试密钥

- `sk-test123456789`
- `sk-demo123456789`
- `sk-your-api-key-here`

## 配置

### API 密钥管理

在 `middleware/auth.go` 中的 `isValidAPIKey` 函数中配置有效的 API 密钥。

在生产环境中，建议：
1. 从数据库或配置文件读取密钥
2. 实现密钥过期机制
3. 添加使用限制和监控

### 端口配置

在 `main.go` 中修改端口：

```go
port := ":8080"  // 修改为您需要的端口
```

## 集成您的 AI 模型

目前 API 返回模拟响应。要集成您的实际 AI 模型：

1. 修改 `handlers/chat.go` 中的 `generateMockResponse` 函数
2. 替换为您的模型调用逻辑
3. 处理流式响应（如果需要）

## 项目结构

```
cassidy2api/
├── main.go              # 主入口文件
├── models/
│   └── openai.go        # OpenAI API 数据结构
├── handlers/
│   ├── chat.go          # 聊天完成处理器
│   └── models.go        # 模型列表处理器
├── middleware/
│   ├── auth.go          # 认证中间件
│   ├── cors.go          # CORS 中间件
│   └── logger.go        # 日志中间件
├── go.mod
└── README.md
```

## 部署

### Docker 部署

创建 `Dockerfile`:

```dockerfile
FROM golang:1.24-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o cassidy2api

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/cassidy2api .
EXPOSE 8080
CMD ["./cassidy2api"]
```

构建和运行：

```bash
docker build -t cassidy2api .
docker run -p 8080:8080 cassidy2api
```

## 许可证

MIT License
