package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"cassidy2api/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ChatCompletions 处理聊天完成请求
func ChatCompletions(c *gin.Context) {
	var req models.ChatCompletionRequest

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.ErrorDetail{
				Message: fmt.Sprintf("Invalid request: %v", err),
				Type:    "invalid_request_error",
			},
		})
		return
	}

	// 验证请求参数
	if err := validateChatRequest(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.ErrorDetail{
				Message: err.Error(),
				Type:    "invalid_request_error",
			},
		})
		return
	}

	// 处理流式响应
	if req.Stream != nil && *req.Stream {
		handleStreamResponse(c, &req)
		return
	}

	// 处理非流式响应
	handleNonStreamResponse(c, &req)
}

// validateChatRequest 验证聊天请求
func validateChatRequest(req *models.ChatCompletionRequest) error {
	if len(req.Messages) == 0 {
		return fmt.Errorf("messages cannot be empty")
	}

	for i, msg := range req.Messages {
		if msg.Role == "" {
			return fmt.Errorf("message[%d].role is required", i)
		}
		if msg.Role != "system" && msg.Role != "user" && msg.Role != "assistant" {
			return fmt.Errorf("message[%d].role must be 'system', 'user', or 'assistant'", i)
		}
		if msg.Content == nil {
			return fmt.Errorf("message[%d].content is required", i)
		}
		// 验证 content 是否为有效的字符串或结构化内容
		if err := validateMessageContent(msg.Content); err != nil {
			return fmt.Errorf("message[%d].content is invalid: %v", i, err)
		}
	}

	return nil
}

// validateMessageContent 验证消息内容
func validateMessageContent(content interface{}) error {
	if content == nil {
		return fmt.Errorf("content cannot be nil")
	}

	switch v := content.(type) {
	case string:
		if v == "" {
			return fmt.Errorf("content cannot be empty string")
		}
	case []interface{}, map[string]interface{}:
		// 支持结构化内容（如图片、文件等）
		return nil
	default:
		return fmt.Errorf("unsupported content type: %T", v)
	}

	return nil
}

// handleNonStreamResponse 处理非流式响应
func handleNonStreamResponse(c *gin.Context, req *models.ChatCompletionRequest) {
	// 这里您可以集成您的实际AI模型
	// 目前返回一个模拟响应
	response := generateMockResponse(req)
	c.JSON(http.StatusOK, response)
}

// handleStreamResponse 处理流式响应
func handleStreamResponse(c *gin.Context, req *models.ChatCompletionRequest) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	// 模拟流式响应
	// 在实际实现中，您需要连接到您的AI模型并流式传输响应
	c.String(http.StatusOK, "data: %s\n\n", `{"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"delta":{"content":"Hello"},"index":0,"finish_reason":null}]}`)
	c.String(http.StatusOK, "data: %s\n\n", `{"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"delta":{"content":" World"},"index":0,"finish_reason":null}]}`)
	c.String(http.StatusOK, "data: %s\n\n", `{"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"delta":{},"index":0,"finish_reason":"stop"}]}`)
	c.String(http.StatusOK, "data: [DONE]\n\n")
}

// generateMockResponse 生成模拟响应
func generateMockResponse(req *models.ChatCompletionRequest) models.ChatCompletion {
	// 获取最后一条用户消息
	var lastUserMessage string
	for i := len(req.Messages) - 1; i >= 0; i-- {
		if req.Messages[i].Role == "user" {
			if content, ok := req.Messages[i].Content.(string); ok {
				lastUserMessage = content
			} else {
				// 如果是结构化内容，转换为JSON字符串
				if contentBytes, err := json.Marshal(req.Messages[i].Content); err == nil {
					lastUserMessage = string(contentBytes)
				} else {
					lastUserMessage = "复杂内容"
				}
			}
			break
		}
	}

	// 生成简单的回复
	responseContent := fmt.Sprintf("您好！我收到了您的消息：%s。这是一个来自 Cassidy2API 的模拟响应。", lastUserMessage)

	return models.ChatCompletion{
		ID:                fmt.Sprintf("chatcmpl-%s", uuid.New().String()[:8]),
		Object:            "chat.completion",
		Created:           int(time.Now().Unix()),
		Model:             req.Model,
		Provider:          "cassidy2api",
		SystemFingerprint: "fp_" + uuid.New().String()[:8],
		Choices: []models.ChatCompletionChoice{
			{
				Index: 0,
				Message: models.ChatMessage{
					Role:    "assistant",
					Content: responseContent,
				},
				FinishReason: "stop",
			},
		},
		Usage: &models.TokenUsage{
			PromptTokens:     countTokens(req.Messages),
			CompletionTokens: countTokens([]models.ChatMessage{{Content: responseContent}}),
			TotalTokens:      countTokens(req.Messages) + countTokens([]models.ChatMessage{{Content: responseContent}}),
		},
	}
}

// countTokens 简单的token计数（实际应用中需要使用真实的tokenizer）
func countTokens(messages []models.ChatMessage) int {
	total := 0
	for _, msg := range messages {
		// 简单估算：每4个字符约等于1个token
		contentStr := ""
		if content, ok := msg.Content.(string); ok {
			contentStr = content
		} else {
			// 如果是结构化内容，转换为JSON字符串来计算长度
			if contentBytes, err := json.Marshal(msg.Content); err == nil {
				contentStr = string(contentBytes)
			}
		}
		total += len(contentStr) / 4
	}
	return total
}
