package main

import (
	"log"
	"net/http"

	"cassidy2api/handlers"
	"cassidy2api/middleware"

	"github.com/gin-gonic/gin"
)

func main() {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	// 创建 Gin 路由器
	r := gin.Default()

	// 添加中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())

	// API v1 路由组
	v1 := r.Group("/v1")
	{
		// 需要认证的路由
		authenticated := v1.Group("")
		authenticated.Use(middleware.AuthMiddleware())
		{
			// 聊天完成接口
			authenticated.POST("/chat/completions", handlers.ChatCompletions)
		}

		// 公开路由
		v1.GET("/models", handlers.ListModels)
	}

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"message": "Cassidy2API is running",
		})
	})

	// 启动服务器
	port := ":8080"
	log.Printf("Server starting on port %s", port)
	if err := r.Run(port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
