package middleware

import (
	"net/http"
	"strings"

	"cassidy2api/models"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware API密钥认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取Authorization
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Error: models.ErrorDetail{
					Message: "Missing Authorization header",
					Type:    "authentication_error",
				},
			})
			c.Abort()
			return
		}

		// 检查Bearer token格式
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSO<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Error: models.ErrorDetail{
					Message: "Invalid Authorization header format. Expected 'Bearer <token>'",
					Type:    "authentication_error",
				},
			})
			c.Abort()
			return
		}

		// 提取token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Error: models.ErrorDetail{
					Message: "Missing API key",
					Type:    "authentication_error",
				},
			})
			c.Abort()
			return
		}

		// 验证token（这里您可以实现自己的验证逻辑）
		if !isValidAPIKey(token) {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error: models.ErrorDetail{
					Message: "Invalid API key",
					Type:    "authentication_error",
				},
			})
			c.Abort()
			return
		}

		// 将token存储在上下文中，供后续处理器使用
		c.Set("api_key", token)
		c.Next()
	}
}

// isValidAPIKey 验证API密钥
// 在实际应用中，您应该从数据库或配置文件中验证密钥
func isValidAPIKey(token string) bool {
	// 这里是一个简单的示例验证
	// 在生产环境中，您应该：
	// 1. 从数据库查询有效的API密钥
	// 2. 检查密钥是否过期
	// 3. 检查密钥的权限范围
	// 4. 记录API使用情况
	
	validKeys := []string{
		"sk-test123456789",
		"sk-demo123456789",
		"sk-your-api-key-here",
	}

	for _, validKey := range validKeys {
		if token == validKey {
			return true
		}
	}

	return false
}
