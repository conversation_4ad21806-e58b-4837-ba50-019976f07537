package models

// ChatMessage 定义聊天消息结构
type ChatMessage struct {
	Content interface{} `json:"content"`
	Name    *string     `json:"name,omitempty"`
	Role    string      `json:"role"`
}

// MessageDelta 定义消息增量变化
type MessageDelta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

// TokenUsage 定义令牌使用情况
type TokenUsage struct {
	CompletionTokens int `json:"completion_tokens"`
	PromptTokens     int `json:"prompt_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ChatCompletionRequest 定义请求结构
type ChatCompletionRequest struct {
	Model               string                   `json:"model"`
	Messages            []ChatMessage            `json:"messages"`
	MaxCompletionTokens *int                     `json:"max_completion_tokens,omitempty"`
	Modalities          []string                 `json:"modalities,omitempty"`
	ResponseFormat      map[string]interface{}   `json:"response_format,omitempty"`
	Seed                *int                     `json:"seed,omitempty"`
	Stop                interface{}              `json:"stop,omitempty"`
	Stream              *bool                    `json:"stream,omitempty"`
	StreamOptions       map[string]interface{}   `json:"stream_options,omitempty"`
	Temperature         *float64                 `json:"temperature,omitempty"`
	ToolChoice          interface{}              `json:"tool_choice,omitempty"`
	Tools               []map[string]interface{} `json:"tools,omitempty"`
	TopK                *int                     `json:"top_k,omitempty"`
	TopP                *float64                 `json:"top_p,omitempty"`
	WebSearchOptions    map[string]interface{}   `json:"web_search_options,omitempty"`
	// VSCode RooCode 特定参数
	N                *int     `json:"n,omitempty"`
	FrequencyPenalty *float64 `json:"frequency_penalty,omitempty"`
	PresencePenalty  *float64 `json:"presence_penalty,omitempty"`
	MaxTokens        *int     `json:"max_tokens,omitempty"`
	User             *string  `json:"user,omitempty"`
}

// ChatCompletionChunkChoice 定义流式响应中的选择项
type ChatCompletionChunkChoice struct {
	Delta        *MessageDelta `json:"delta"`
	FinishReason string        `json:"finish_reason,omitempty"`
	Index        int           `json:"index"`
}

// ChatCompletionChunk 定义流式响应的结构
type ChatCompletionChunk struct {
	ID                string                      `json:"id"`
	Object            string                      `json:"object"`
	Created           int                         `json:"created"`
	Model             string                      `json:"model"`
	Provider          string                      `json:"provider"`
	Choices           []ChatCompletionChunkChoice `json:"choices"`
	SystemFingerprint string                      `json:"system_fingerprint"`
	Usage             *TokenUsage                 `json:"usage,omitempty"`
}

// ChatCompletionChoice 定义非流式响应的选择项
type ChatCompletionChoice struct {
	FinishReason string                 `json:"finish_reason"`
	Index        int                    `json:"index"`
	Logprobs     map[string]interface{} `json:"logprobs,omitempty"`
	Message      ChatMessage            `json:"message"`
}

// ChatCompletion 定义非流式响应的结构
type ChatCompletion struct {
	Choices           []ChatCompletionChoice `json:"choices"`
	Created           int                    `json:"created"`
	ID                string                 `json:"id"`
	Model             string                 `json:"model"`
	Object            string                 `json:"object"`
	ServiceTier       *string                `json:"service_tier,omitempty"`
	SystemFingerprint string                 `json:"system_fingerprint"`
	Usage             *TokenUsage            `json:"usage,omitempty"`
	Provider          string                 `json:"provider,omitempty"`
}

// Model 定义模型信息
type Model struct {
	Created int    `json:"created"`
	ID      string `json:"id"`
	Object  string `json:"object"`
	OwnedBy string `json:"owned_by"`
}
